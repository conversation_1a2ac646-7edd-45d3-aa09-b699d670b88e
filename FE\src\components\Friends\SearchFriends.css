.search-friends-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  animation: modalSlideIn 0.3s ease-out;
}

.search-form {
  margin-bottom: 24px;
}

.input-group {
  display: flex;
  gap: 8px;
}

.input-group input {
  flex: 1;
}

.search-btn {
  width: 44px;
  height: 44px;
  border: none;
  border-radius: 8px;
  background: #0084ff;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.search-btn:hover:not(:disabled) {
  background: #0066cc;
}

.search-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #0084ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner.small {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #fee;
  color: #c33;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #fcc;
  font-size: 14px;
}

.search-result {
  margin-bottom: 24px;
}

.user-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 1px solid #e5e5e5;
  border-radius: 12px;
  background: #f8f9fa;
}

.user-avatar {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 20px;
  color: white;
  flex-shrink: 0;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.user-info p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.user-actions {
  flex-shrink: 0;
}

.friend-request-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #0084ff;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.friend-request-btn:hover:not(:disabled) {
  background: #0066cc;
}

.friend-request-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.friendship-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #e9ecef;
  color: #666;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
}

.search-tips {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #0084ff;
}

.search-tips h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.search-tips ul {
  margin: 0;
  padding-left: 20px;
  color: #666;
}

.search-tips li {
  margin-bottom: 4px;
  font-size: 13px;
}

.search-tips li:last-child {
  margin-bottom: 0;
}

/* Responsive */
@media (max-width: 480px) {
  .search-friends-modal {
    width: 95%;
    margin: 20px;
  }
  
  .user-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .user-info {
    text-align: center;
  }
  
  .input-group {
    flex-direction: column;
  }
  
  .search-btn {
    width: 100%;
    height: 44px;
  }
}
