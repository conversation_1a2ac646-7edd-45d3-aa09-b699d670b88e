import { useState, useEffect } from 'react';
import { userAPI, conversationAPI } from '../../services/api';
import './AddMembersModal.css';

const AddMembersModal = ({ conversation, onClose, onMembersAdded }) => {
  const [friends, setFriends] = useState([]);
  const [currentMembers, setCurrentMembers] = useState([]);
  const [selectedFriends, setSelectedFriends] = useState(new Set());
  const [loading, setLoading] = useState(true);
  const [adding, setAdding] = useState(false);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchData();
  }, [conversation.id]);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      // Fetch friends và current members song song
      const [friendsResponse, membersResponse] = await Promise.all([
        userAPI.getFriends(),
        conversationAPI.getMembers(conversation.id)
      ]);
      
      setFriends(friendsResponse.data.friends);
      setCurrentMembers(membersResponse.data);
    } catch (err) {
      setError('Không thể tải dữ liệu');
      console.error('Error fetching data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectFriend = (friendId) => {
    const newSelected = new Set(selectedFriends);
    if (newSelected.has(friendId)) {
      newSelected.delete(friendId);
    } else {
      newSelected.add(friendId);
    }
    setSelectedFriends(newSelected);
  };

  const handleAddMembers = async () => {
    if (selectedFriends.size === 0) {
      alert('Vui lòng chọn ít nhất một bạn bè để thêm');
      return;
    }

    setAdding(true);
    const addedMembers = [];
    const errors = [];

    try {
      // Thêm từng member một cách tuần tự
      for (const friendId of selectedFriends) {
        try {
          await conversationAPI.addMember(conversation.id, friendId);
          const friend = friends.find(f => f.id === friendId);
          if (friend) {
            addedMembers.push(friend);
          }
        } catch (err) {
          const friend = friends.find(f => f.id === friendId);
          errors.push(`${friend?.username || 'Unknown'}: ${err.response?.data?.message || 'Lỗi không xác định'}`);
        }
      }

      if (addedMembers.length > 0) {
        // Callback để update conversation list
        if (onMembersAdded) {
          onMembersAdded(addedMembers);
        }
        
        if (errors.length === 0) {
          alert(`Đã thêm ${addedMembers.length} thành viên thành công!`);
          onClose();
        } else {
          alert(`Đã thêm ${addedMembers.length} thành viên thành công.\nLỗi: ${errors.join(', ')}`);
        }
      } else {
        alert(`Không thể thêm thành viên nào.\nLỗi: ${errors.join(', ')}`);
      }
    } catch (err) {
      alert('Có lỗi xảy ra khi thêm thành viên');
    } finally {
      setAdding(false);
    }
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Filter friends: loại bỏ những người đã là member và filter theo search term
  const currentMemberIds = new Set(currentMembers.map(m => m.id));
  const availableFriends = friends.filter(friend => 
    !currentMemberIds.has(friend.id) &&
    (friend.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
     friend.phoneNumber.includes(searchTerm))
  );

  return (
    <div className="modal-backdrop" onClick={handleBackdropClick}>
      <div className="add-members-modal">
        <div className="modal-header">
          <h3>Thêm thành viên vào "{conversation.name}"</h3>
          <button className="close-btn" onClick={onClose}>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>

        <div className="modal-body">
          {loading ? (
            <div className="loading-state">
              <div className="spinner"></div>
              <p>Đang tải danh sách bạn bè...</p>
            </div>
          ) : error ? (
            <div className="error-state">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
              <p>{error}</p>
              <button className="retry-btn" onClick={fetchData}>
                Thử lại
              </button>
            </div>
          ) : (
            <>
              {availableFriends.length > 0 && (
                <div className="search-box">
                  <svg className="search-icon" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                  </svg>
                  <input
                    type="text"
                    placeholder="Tìm kiếm bạn bè..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              )}

              {selectedFriends.size > 0 && (
                <div className="selected-count">
                  Đã chọn {selectedFriends.size} bạn bè
                </div>
              )}

              {availableFriends.length === 0 ? (
                <div className="empty-state">
                  <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"/>
                  </svg>
                  <h4>Không có bạn bè nào để thêm</h4>
                  <p>Tất cả bạn bè đã là thành viên của nhóm này hoặc bạn chưa có bạn bè nào.</p>
                </div>
              ) : (
                <div className="friends-list">
                  {availableFriends.map(friend => (
                    <div
                      key={friend.id}
                      className={`friend-item ${selectedFriends.has(friend.id) ? 'selected' : ''}`}
                      onClick={() => handleSelectFriend(friend.id)}
                    >
                      <div className="friend-avatar">
                        {friend.username.charAt(0).toUpperCase()}
                      </div>
                      
                      <div className="friend-info">
                        <h4>{friend.username}</h4>
                        <p>{friend.phoneNumber}</p>
                      </div>
                      
                      <div className="selection-indicator">
                        {selectedFriends.has(friend.id) ? (
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                          </svg>
                        ) : (
                          <div className="empty-circle"></div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </>
          )}
        </div>

        {!loading && !error && availableFriends.length > 0 && (
          <div className="modal-footer">
            <button 
              className="cancel-btn"
              onClick={onClose}
              disabled={adding}
            >
              Hủy
            </button>
            <button 
              className="add-btn"
              onClick={handleAddMembers}
              disabled={adding || selectedFriends.size === 0}
            >
              {adding ? (
                <>
                  <div className="spinner small"></div>
                  Đang thêm...
                </>
              ) : (
                `Thêm ${selectedFriends.size > 0 ? selectedFriends.size : ''} thành viên`
              )}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default AddMembersModal;
