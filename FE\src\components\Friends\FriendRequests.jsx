import { useState, useEffect } from 'react';
import { userAPI } from '../../services/api';
import './FriendRequests.css';

const FriendRequests = ({ onClose }) => {
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [processingRequests, setProcessingRequests] = useState(new Set());

  useEffect(() => {
    fetchFriendRequests();
  }, []);

  const fetchFriendRequests = async () => {
    try {
      setLoading(true);
      const response = await userAPI.getFriendRequests();
      setRequests(response.data.requests);
    } catch (err) {
      setError('Không thể tải danh sách lời mời kết bạn');
      console.error('Error fetching friend requests:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleAcceptRequest = async (requestId) => {
    setProcessingRequests(prev => new Set(prev).add(requestId));
    
    try {
      await userAPI.acceptFriendRequest(requestId);
      
      // Xóa request khỏi danh sách
      setRequests(prev => prev.filter(req => req.id !== requestId));
      
      // Có thể thêm notification hoặc callback để update friend list
    } catch (err) {
      alert(err.response?.data?.message || 'Không thể chấp nhận lời mời kết bạn');
    } finally {
      setProcessingRequests(prev => {
        const newSet = new Set(prev);
        newSet.delete(requestId);
        return newSet;
      });
    }
  };

  const handleRejectRequest = async (requestId) => {
    setProcessingRequests(prev => new Set(prev).add(requestId));
    
    try {
      await userAPI.rejectFriendRequest(requestId);
      
      // Xóa request khỏi danh sách
      setRequests(prev => prev.filter(req => req.id !== requestId));
    } catch (err) {
      alert(err.response?.data?.message || 'Không thể từ chối lời mời kết bạn');
    } finally {
      setProcessingRequests(prev => {
        const newSet = new Set(prev);
        newSet.delete(requestId);
        return newSet;
      });
    }
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
      return 'Vừa xong';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)} giờ trước`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      if (diffInDays === 1) {
        return 'Hôm qua';
      } else if (diffInDays < 7) {
        return `${diffInDays} ngày trước`;
      } else {
        return date.toLocaleDateString('vi-VN');
      }
    }
  };

  return (
    <div className="modal-backdrop" onClick={handleBackdropClick}>
      <div className="friend-requests-modal">
        <div className="modal-header">
          <h3>Lời mời kết bạn</h3>
          <button className="close-btn" onClick={onClose}>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>

        <div className="modal-body">
          {loading ? (
            <div className="loading-state">
              <div className="spinner"></div>
              <p>Đang tải danh sách lời mời...</p>
            </div>
          ) : error ? (
            <div className="error-state">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
              <p>{error}</p>
              <button className="retry-btn" onClick={fetchFriendRequests}>
                Thử lại
              </button>
            </div>
          ) : requests.length === 0 ? (
            <div className="empty-state">
              <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
                <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A3.002 3.002 0 0 0 17 6c-1.66 0-3 1.34-3 3 0 .35.07.69.18 1H12c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3h2c0-2.76-2.24-5-5-5S7 1.24 7 4s2.24 5 5 5h2.18c.11.31.18.65.18 1v2H9v6h2v4h2v-4h2v4h2v-4h3z"/>
              </svg>
              <h4>Không có lời mời kết bạn nào</h4>
              <p>Khi có người gửi lời mời kết bạn, chúng sẽ hiển thị ở đây</p>
            </div>
          ) : (
            <div className="requests-list">
              {requests.map(request => (
                <div key={request.id} className="request-item">
                  <div className="request-avatar">
                    {request.fromUser.username.charAt(0).toUpperCase()}
                  </div>
                  
                  <div className="request-info">
                    <h4>{request.fromUser.username}</h4>
                    <p className="phone-number">{request.fromUser.phoneNumber}</p>
                    <p className="request-time">{formatDate(request.createdAt)}</p>
                  </div>
                  
                  <div className="request-actions">
                    <button 
                      className="accept-btn"
                      onClick={() => handleAcceptRequest(request.id)}
                      disabled={processingRequests.has(request.id)}
                    >
                      {processingRequests.has(request.id) ? (
                        <div className="spinner small"></div>
                      ) : (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                        </svg>
                      )}
                    </button>
                    
                    <button 
                      className="reject-btn"
                      onClick={() => handleRejectRequest(request.id)}
                      disabled={processingRequests.has(request.id)}
                    >
                      {processingRequests.has(request.id) ? (
                        <div className="spinner small"></div>
                      ) : (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                        </svg>
                      )}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FriendRequests;
