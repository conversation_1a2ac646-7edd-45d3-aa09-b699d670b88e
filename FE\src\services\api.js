import axios from "axios";

const API_BASE_URL = "http://localhost:3000/api";

// Tạo axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Interceptor để tự động thêm token vào headers
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Interceptor để handle response errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired hoặc invalid
      localStorage.removeItem("token");
      localStorage.removeItem("user");
      window.location.href = "/login";
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post("/auth/login", credentials),
  register: (userData) => api.post("/auth/register", userData),
};

// User API
export const userAPI = {
  searchByPhone: (phoneNumber) =>
    api.get(`/users/search?phoneNumber=${phoneNumber}`),
  sendFriendRequest: (toUserID) =>
    api.post("/users/friend-request", { toUserID }),
  getFriendRequests: () => api.get("/users/friend-requests"),
  acceptFriendRequest: (requestId) =>
    api.put(`/users/friend-request/${requestId}/accept`),
  rejectFriendRequest: (requestId) =>
    api.put(`/users/friend-request/${requestId}/reject`),
  getFriends: () => api.get("/users/friends"),
};

// Conversation API
export const conversationAPI = {
  getAll: () => api.get("/conversations/conversations"),
  create: (data) => api.post("/conversations/conversations", data),
  addMember: (conversationId, userId) =>
    api.post(`/conversations/conversations/${conversationId}/members`, {
      userId,
    }),
  getMembers: (conversationId) =>
    api.get(`/conversations/conversations/${conversationId}/members`),
};

// Message API
export const messageAPI = {
  getMessages: (conversationId) => api.get(`/messages/${conversationId}`),
  sendMessage: (conversationId, content) =>
    api.post(`/messages/${conversationId}`, { content }),
};

export default api;
