.conversation-list {
  width: 320px;
  height: 100vh;
  background: #fff;
  border-right: 1px solid #e5e5e5;
  display: flex;
  flex-direction: column;
}

.conversation-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
}

.conversation-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.create-group-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  background: #0084ff;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.create-group-btn:hover {
  background: #0066cc;
}

.search-box {
  padding: 12px 20px;
  border-bottom: 1px solid #e5e5e5;
  position: relative;
}

.search-box input {
  width: 100%;
  padding: 8px 12px 8px 36px;
  border: 1px solid #ddd;
  border-radius: 20px;
  font-size: 14px;
  background: #f5f5f5;
  outline: none;
  transition: border-color 0.2s ease;
}

.search-box input:focus {
  border-color: #0084ff;
  background: white;
}

.search-icon {
  position: absolute;
  left: 32px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
}

.conversations {
  flex: 1;
  overflow-y: auto;
}

.conversation-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
  position: relative;
}

.conversation-item:hover {
  background: #f8f9fa;
}

.conversation-item.active {
  background: #e3f2fd;
  border-right: 3px solid #0084ff;
}

.conversation-avatar {
  margin-right: 12px;
  flex-shrink: 0;
}

.user-avatar, .group-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 18px;
  color: white;
}

.user-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.group-avatar {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.conversation-info {
  flex: 1;
  min-width: 0;
}

.conversation-name {
  font-weight: 600;
  font-size: 15px;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.last-message {
  font-size: 13px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-left: 8px;
}

.last-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.loading {
  padding: 40px 20px;
  text-align: center;
  color: #666;
}

.error-message {
  padding: 12px 20px;
  background: #fee;
  color: #c33;
  border-bottom: 1px solid #fcc;
  font-size: 14px;
}

.no-conversations {
  padding: 40px 20px;
  text-align: center;
  color: #666;
}

.no-conversations p {
  margin-bottom: 16px;
  font-size: 15px;
}

.create-first-group {
  padding: 10px 20px;
  background: #0084ff;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.create-first-group:hover {
  background: #0066cc;
}

/* Scrollbar styling */
.conversations::-webkit-scrollbar {
  width: 6px;
}

.conversations::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.conversations::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.conversations::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive */
@media (max-width: 768px) {
  .conversation-list {
    width: 100%;
  }
  
  .conversation-header {
    padding: 12px 16px;
  }
  
  .search-box {
    padding: 8px 16px;
  }
  
  .conversation-item {
    padding: 10px 16px;
  }
}
