.add-members-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  animation: modalSlideIn 0.3s ease-out;
  display: flex;
  flex-direction: column;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 400px;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
}

.close-btn:hover {
  background: #f5f5f5;
}

.modal-body {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.loading-state, .error-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  flex: 1;
}

.loading-state .spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #0084ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-state p, .error-state p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.error-state svg, .empty-state svg {
  color: #ccc;
  margin-bottom: 16px;
}

.empty-state h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #333;
}

.empty-state p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.retry-btn {
  margin-top: 16px;
  padding: 10px 20px;
  background: #0084ff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.retry-btn:hover {
  background: #0066cc;
}

.search-box {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e5e5;
  position: relative;
  flex-shrink: 0;
}

.search-box input {
  width: 100%;
  padding: 10px 12px 10px 36px;
  border: 1px solid #ddd;
  border-radius: 20px;
  font-size: 14px;
  background: #f5f5f5;
  outline: none;
  transition: border-color 0.2s ease;
}

.search-box input:focus {
  border-color: #0084ff;
  background: white;
}

.search-icon {
  position: absolute;
  left: 32px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
}

.selected-count {
  padding: 12px 20px;
  background: #e3f2fd;
  border-bottom: 1px solid #e5e5e5;
  font-size: 14px;
  color: #0084ff;
  font-weight: 500;
  flex-shrink: 0;
}

.friends-list {
  flex: 1;
  overflow-y: auto;
  max-height: 300px;
}

.friend-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.friend-item:hover {
  background: #f8f9fa;
}

.friend-item.selected {
  background: #e3f2fd;
  border-left: 4px solid #0084ff;
}

.friend-item:last-child {
  border-bottom: none;
}

.friend-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 18px;
  color: white;
  flex-shrink: 0;
}

.friend-info {
  flex: 1;
  min-width: 0;
}

.friend-info h4 {
  margin: 0 0 4px 0;
  font-size: 15px;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.friend-info p {
  margin: 0;
  font-size: 13px;
  color: #666;
}

.selection-indicator {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selection-indicator svg {
  color: #0084ff;
}

.empty-circle {
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-radius: 50%;
  transition: border-color 0.2s ease;
}

.friend-item:hover .empty-circle {
  border-color: #0084ff;
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #e5e5e5;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  flex-shrink: 0;
}

.cancel-btn, .add-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.cancel-btn:hover:not(:disabled) {
  background: #e9ecef;
}

.add-btn {
  background: #0084ff;
  color: white;
}

.add-btn:hover:not(:disabled) {
  background: #0066cc;
}

.add-btn:disabled, .cancel-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #0084ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner.small {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Scrollbar styling */
.friends-list::-webkit-scrollbar {
  width: 6px;
}

.friends-list::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.friends-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.friends-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive */
@media (max-width: 480px) {
  .add-members-modal {
    width: 95%;
    margin: 20px;
  }
  
  .modal-header {
    padding: 16px 20px;
  }
  
  .modal-header h3 {
    font-size: 16px;
    max-width: 250px;
  }
  
  .search-box {
    padding: 12px 16px;
  }
  
  .selected-count {
    padding: 10px 16px;
  }
  
  .friend-item {
    padding: 12px 16px;
  }
  
  .friend-avatar {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
  
  .friend-info h4 {
    font-size: 14px;
  }
  
  .friend-info p {
    font-size: 12px;
  }
  
  .modal-footer {
    padding: 12px 20px;
    flex-direction: column;
  }
  
  .cancel-btn, .add-btn {
    width: 100%;
    justify-content: center;
    padding: 12px;
  }
}
