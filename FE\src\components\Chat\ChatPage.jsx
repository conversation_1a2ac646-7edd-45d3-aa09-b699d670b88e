import { useState, useEffect } from "react";
import { authUtils } from "../../utils/auth";
import { useNavigate } from "react-router-dom";
import ConversationList from "./ConversationList";
import ChatWindow from "./ChatWindow";
import "./ChatPage.css";

const ChatPage = () => {
  const [user, setUser] = useState(null);
  const [selectedConversation, setSelectedConversation] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    const currentUser = authUtils.getUser();
    if (currentUser) {
      setUser(currentUser);
    }
  }, []);

  const handleLogout = () => {
    authUtils.logout();
    navigate("/login");
  };

  const handleSelectConversation = (conversation) => {
    setSelectedConversation(conversation);
  };

  return (
    <div className="chat-page">
      <div className="chat-header">
        <div className="header-left">
          <h1>Chat Web</h1>
        </div>
        <div className="header-right">
          {user && (
            <div className="user-info">
              <span className="username">
                Xin chào, <strong>{user.username}</strong>
              </span>
              <button
                onClick={handleLogout}
                className="logout-btn"
                title="Đăng xuất"
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z" />
                </svg>
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="chat-content">
        <ConversationList
          onSelectConversation={handleSelectConversation}
          selectedConversationId={selectedConversation?.id}
        />
        <ChatWindow selectedConversation={selectedConversation} />
      </div>
    </div>
  );
};

export default ChatPage;
