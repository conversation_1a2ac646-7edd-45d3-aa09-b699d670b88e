import { io } from 'socket.io-client';
import { authUtils } from '../utils/auth';

class SocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.messageHandlers = new Set();
    this.typingHandlers = new Set();
    this.statusHandlers = new Set();
  }

  connect() {
    const token = authUtils.getToken();
    if (!token) {
      console.error('No token found for socket connection');
      return;
    }

    if (this.socket && this.isConnected) {
      console.log('Socket already connected');
      return;
    }

    this.socket = io('http://localhost:3001', {
      auth: {
        token: token
      },
      autoConnect: true
    });

    this.setupEventListeners();
  }

  setupEventListeners() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Socket connected:', this.socket.id);
      this.isConnected = true;
    });

    this.socket.on('disconnect', () => {
      console.log('Socket disconnected');
      this.isConnected = false;
    });

    this.socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      this.isConnected = false;
    });

    // Message events
    this.socket.on('new-message', (message) => {
      console.log('New message received:', message);
      this.messageHandlers.forEach(handler => handler(message));
    });

    // Typing events
    this.socket.on('user-typing', (data) => {
      this.typingHandlers.forEach(handler => handler(data, 'start'));
    });

    this.socket.on('user-stop-typing', (data) => {
      this.typingHandlers.forEach(handler => handler(data, 'stop'));
    });

    // Status events
    this.socket.on('user-status-change', (data) => {
      this.statusHandlers.forEach(handler => handler(data));
    });

    this.socket.on('joined-conversations', (conversationIds) => {
      console.log('Successfully joined conversations:', conversationIds);
    });

    this.socket.on('error', (error) => {
      console.error('Socket error:', error);
    });
  }

  joinConversations(conversationIds) {
    if (!this.socket || !this.isConnected) {
      console.error('Socket not connected');
      return;
    }

    this.socket.emit('join-conversations', conversationIds);
  }

  startTyping(conversationId) {
    if (!this.socket || !this.isConnected) return;
    
    this.socket.emit('typing-start', { conversationId });
  }

  stopTyping(conversationId) {
    if (!this.socket || !this.isConnected) return;
    
    this.socket.emit('typing-stop', { conversationId });
  }

  // Event handlers management
  onMessage(handler) {
    this.messageHandlers.add(handler);
    
    // Return unsubscribe function
    return () => {
      this.messageHandlers.delete(handler);
    };
  }

  onTyping(handler) {
    this.typingHandlers.add(handler);
    
    return () => {
      this.typingHandlers.delete(handler);
    };
  }

  onStatusChange(handler) {
    this.statusHandlers.add(handler);
    
    return () => {
      this.statusHandlers.delete(handler);
    };
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
    
    // Clear all handlers
    this.messageHandlers.clear();
    this.typingHandlers.clear();
    this.statusHandlers.clear();
  }

  // Utility methods
  isSocketConnected() {
    return this.isConnected && this.socket && this.socket.connected;
  }

  getSocketId() {
    return this.socket?.id;
  }
}

// Create singleton instance
const socketService = new SocketService();

export default socketService;
