.message-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
  background: linear-gradient(to bottom, #f8f9fa 0%, #e9ecef 100%);
}

.loading-spinner,
.error-state,
.empty-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: #65676b;
}

.loading-spinner .spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1877f2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.error-state svg,
.empty-messages svg {
  color: #ccc;
  margin-bottom: 16px;
}

.error-state h3,
.empty-messages h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #1c1e21;
  font-weight: 600;
}

.error-state p,
.empty-messages p {
  margin: 0;
  font-size: 14px;
  line-height: 1.4;
  color: #65676b;
}

.retry-btn {
  margin-top: 16px;
  padding: 10px 20px;
  background: #1877f2;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.retry-btn:hover {
  background: #166fe5;
}

.date-separator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 16px 0;
  position: relative;
}

.date-separator::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e4e6eb;
  z-index: 1;
}

.date-separator span {
  background: white;
  padding: 4px 12px;
  font-size: 12px;
  color: #65676b;
  border-radius: 12px;
  border: 1px solid #e4e6eb;
  z-index: 2;
  position: relative;
  font-weight: 500;
}

.message {
  display: flex;
  align-items: flex-start;
  padding: 4px 16px;
  animation: messageSlideIn 0.3s ease-out;
  transition: background-color 0.2s ease;
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

.message:hover {
  background: rgba(0, 0, 0, 0.02);
}

.message.own {
  background: transparent;
  justify-content: flex-end;
  padding-left: 60px;
  padding-right: 16px;
  flex-direction: row-reverse;
}

.message.own:hover {
  background: rgba(227, 242, 253, 0.3);
}

.message.other {
  background: transparent;
  justify-content: flex-start;
  padding-right: 60px;
  padding-left: 16px;
  flex-direction: row;
}

.message-content {
  display: flex;
  flex-direction: column;
  min-width: 0;
  position: relative;
  max-width: 60%;
  flex: 1;
}

.message.own .message-content {
  align-items: flex-end;
  order: 1;
}

.message.other .message-content {
  align-items: flex-start;
  order: 2;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex !important;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  color: white;
  flex-shrink: 0;
  margin: 0 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
  visibility: visible !important;
  opacity: 1 !important;
}

.message.own .message-avatar {
  order: 2;
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
}

.message.other .message-avatar {
  order: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.message-header {
  display: flex;
  align-items: baseline;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 12px;
}

.message.own .message-header {
  flex-direction: row-reverse;
  justify-content: flex-start;
}

.message.other .message-header {
  flex-direction: row;
  justify-content: flex-start;
}

.message-sender {
  font-size: 14px;
  color: #1c1e21;
  font-weight: 600;
  cursor: pointer;
}

.message.own .message-sender {
  display: none;
}

.message.other .message-sender:hover {
  text-decoration: underline;
  color: #1877f2;
}

.message-timestamp {
  font-size: 11px;
  color: #65676b;
  font-weight: 500;
}

.message.own .message-timestamp {
  color: #1565c0;
  opacity: 0.8;
}

.message.other .message-timestamp {
  color: #65676b;
}

.message-text {
  font-size: 14px;
  line-height: 1.4;
  color: #1c1e21;
  word-wrap: break-word;
  white-space: pre-wrap;
  margin: 0;
  padding: 8px 12px;
  border-radius: 18px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
  max-width: 100%;
}

/* Tin nhắn của mình - bên phải, màu xanh trời nhẹ */
.message.own .message-text {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1565c0;
  border-bottom-right-radius: 4px;
}

/* Tin nhắn nhận được - bên trái, màu trắng */
.message.other .message-text {
  background: #ffffff;
  color: #1c1e21;
  border-bottom-left-radius: 4px;
  border: 1px solid #e4e6eb;
}

/* Hover effects */
.message.own .message-text:hover {
  background: linear-gradient(135deg, #d1e7dd 0%, #a7d3f4 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2);
}

.message.other .message-text:hover {
  background: #f8f9fa;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Animation cho tin nhắn mới */
.message-text {
  transition: all 0.2s ease;
}

.message.own .message-text {
  background: #f0f2f5;
  color: #1c1e21;
  border-bottom-right-radius: 4px;
}

.message.other .message-text {
  background: white;
  border-bottom-left-radius: 4px;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 8px 0;
  opacity: 0.8;
}

.typing-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f1f3f4;
  display: flex;
  align-items: center;
  justify-content: center;
}

.typing-dots {
  display: flex;
  gap: 2px;
}

.typing-dots span {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: #65676b;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

.typing-text {
  font-size: 12px;
  color: #65676b;
  font-style: italic;
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scrollbar styling */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.messages-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive */
@media (max-width: 768px) {
  .messages-container {
    padding: 12px 16px;
    gap: 6px;
  }

  .message {
    margin-bottom: 1px;
  }

  .message.own {
    padding-left: 50px;
    padding-right: 12px;
  }

  .message.other {
    padding-right: 50px;
    padding-left: 12px;
  }

  .message-content {
    max-width: 75%;
  }

  .message-avatar {
    width: 28px;
    height: 28px;
    font-size: 12px;
    margin: 0 6px;
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  .message-text {
    padding: 6px 10px;
    font-size: 13px;
    line-height: 1.3;
  }

  .date-separator span {
    font-size: 11px;
    padding: 3px 10px;
  }

  .message-sender {
    font-size: 11px;
    margin-bottom: 1px;
  }
}

@media (max-width: 480px) {
  .messages-container {
    padding: 8px 12px;
  }

  .message {
    padding: 2px 12px;
  }

  .message.own {
    padding-left: 40px;
    padding-right: 8px;
  }

  .message.other {
    padding-right: 40px;
    padding-left: 8px;
  }

  .message-avatar {
    width: 24px;
    height: 24px;
    font-size: 10px;
    margin: 0 4px;
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  .message-content {
    max-width: 80%;
  }

  .message-text {
    padding: 5px 8px;
    font-size: 12px;
  }
}
