.chat-window {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.welcome-screen {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.welcome-content {
  text-align: center;
  max-width: 400px;
  padding: 40px;
}

.welcome-icon {
  margin-bottom: 24px;
  color: #0084ff;
  opacity: 0.8;
}

.welcome-content h2 {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.welcome-content p {
  font-size: 16px;
  color: #666;
  margin-bottom: 32px;
}

.features {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  font-size: 14px;
  color: #555;
}

.feature-item svg {
  color: #0084ff;
}

.chat-header {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.chat-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chat-avatar .user-avatar,
.chat-avatar .group-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
  color: white;
}

.chat-avatar .user-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.chat-avatar .group-avatar {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.chat-details h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.member-count {
  font-size: 12px;
  color: #666;
}

.chat-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  background: #f5f5f5;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.action-btn:hover {
  background: #e9ecef;
}

.messages-container {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-messages {
  text-align: center;
  color: #666;
}

.no-messages p {
  margin: 8px 0;
  font-size: 14px;
}

.message-input-container {
  padding: 16px 20px;
  border-top: 1px solid #e5e5e5;
  background: #fff;
}

.message-input {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border: 1px solid #e5e5e5;
  border-radius: 24px;
  background: #f8f9fa;
}

.attach-btn, .send-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: none;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.attach-btn:hover {
  background: #e9ecef;
}

.send-btn {
  background: #0084ff;
  color: white;
}

.send-btn:hover:not(:disabled) {
  background: #0066cc;
}

.send-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.message-input input {
  flex: 1;
  border: none;
  background: none;
  outline: none;
  font-size: 14px;
  padding: 8px 0;
}

.message-input input::placeholder {
  color: #999;
}

.message-input input:disabled {
  color: #ccc;
}

/* Responsive */
@media (max-width: 768px) {
  .welcome-content {
    padding: 20px;
  }
  
  .welcome-content h2 {
    font-size: 24px;
  }
  
  .features {
    gap: 12px;
  }
  
  .feature-item {
    padding: 10px 16px;
    font-size: 13px;
  }
  
  .chat-header {
    padding: 0 16px;
  }
  
  .messages-container {
    padding: 16px;
  }
  
  .message-input-container {
    padding: 12px 16px;
  }
}
