.message-input-container {
  padding: 16px 20px;
  border-top: 1px solid #e4e6eb;
  background: #fff;
  flex-shrink: 0;
}

.message-input {
  display: flex;
  align-items: flex-end;
  gap: 10px;
  padding: 6px 8px;
  border: 1.5px solid #e4e6eb;
  border-radius: 25px;
  background: #f0f2f5;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.message-input:focus-within {
  border-color: #1877f2;
  background: white;
  box-shadow: 0 2px 8px rgba(24, 119, 242, 0.15);
}

.attach-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: none;
  color: #65676b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.attach-btn:hover:not(:disabled) {
  background: #e4e6eb;
  color: #1877f2;
}

.attach-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.input-wrapper {
  flex: 1;
  min-width: 0;
}

.input-wrapper textarea {
  width: 100%;
  border: none;
  background: none;
  outline: none;
  font-size: 14px;
  font-family: inherit;
  line-height: 1.4;
  padding: 6px 0;
  resize: none;
  overflow-y: auto;
  color: #1c1e21;
}

.input-wrapper textarea::placeholder {
  color: #65676b;
}

.input-wrapper textarea:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.send-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  background: #ccc;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.send-btn.active {
  background: linear-gradient(135deg, #1877f2 0%, #166fe5 100%);
  transform: scale(1.05);
  box-shadow: 0 3px 8px rgba(24, 119, 242, 0.3);
}

.send-btn.active:hover:not(:disabled) {
  background: linear-gradient(135deg, #166fe5 0%, #1464d9 100%);
  transform: scale(1.08);
}

.send-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.send-btn .spinner.small {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.character-count {
  text-align: right;
  font-size: 11px;
  color: #65676b;
  margin-top: 4px;
  padding-right: 8px;
  font-weight: 500;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Scrollbar for textarea */
.input-wrapper textarea::-webkit-scrollbar {
  width: 4px;
}

.input-wrapper textarea::-webkit-scrollbar-track {
  background: transparent;
}

.input-wrapper textarea::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 2px;
}

.input-wrapper textarea::-webkit-scrollbar-thumb:hover {
  background: #999;
}

/* Responsive */
@media (max-width: 768px) {
  .message-input-container {
    padding: 12px 16px;
  }

  .message-input {
    padding: 6px 10px;
    gap: 10px;
  }

  .attach-btn,
  .send-btn {
    width: 28px;
    height: 28px;
  }

  .attach-btn svg,
  .send-btn svg {
    width: 18px;
    height: 18px;
  }

  .input-wrapper textarea {
    font-size: 13px;
    padding: 4px 0;
  }

  .character-count {
    font-size: 10px;
    margin-top: 2px;
    padding-right: 6px;
  }
}

/* Focus states for accessibility */
.attach-btn:focus,
.send-btn:focus {
  outline: 2px solid #1877f2;
  outline-offset: 2px;
}

.input-wrapper textarea:focus {
  outline: none;
}

/* Animation for send button activation */
@keyframes buttonPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1.05);
  }
}

.send-btn.active {
  animation: buttonPulse 0.3s ease-out;
}
