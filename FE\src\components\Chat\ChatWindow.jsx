import './ChatWindow.css';

const ChatWindow = ({ selectedConversation }) => {
  if (!selectedConversation) {
    return (
      <div className="chat-window">
        <div className="welcome-screen">
          <div className="welcome-content">
            <div className="welcome-icon">
              <svg width="80" height="80" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
              </svg>
            </div>
            <h2>Chào mừng đến với Chat Web!</h2>
            <p>Chọn một cuộc trò chuyện để bắt đầu nhắn tin</p>
            <div className="features">
              <div className="feature-item">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
                <span>Tin nhắn real-time</span>
              </div>
              <div className="feature-item">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"/>
                </svg>
                <span>Chat nhóm</span>
              </div>
              <div className="feature-item">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.1 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z"/>
                </svg>
                <span>Lịch sử tin nhắn</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="chat-window">
      <div className="chat-header">
        <div className="chat-info">
          <div className="chat-avatar">
            {selectedConversation.isGroup ? (
              <div className="group-avatar">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A3.002 3.002 0 0 0 17 6c-1.66 0-3 1.34-3 3 0 .35.07.69.18 1H12c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3h2c0-2.76-2.24-5-5-5S7 1.24 7 4s2.24 5 5 5h2.18c.***********.18 1v2H9v6h2v4h2v-4h2v4h2v-4h3z"/>
                </svg>
              </div>
            ) : (
              <div className="user-avatar">
                {selectedConversation.name?.charAt(0).toUpperCase() || 'U'}
              </div>
            )}
          </div>
          <div className="chat-details">
            <h3>{selectedConversation.name || 'Cuộc trò chuyện'}</h3>
            <span className="member-count">
              {selectedConversation.memberships?.length || 0} thành viên
            </span>
          </div>
        </div>
        <div className="chat-actions">
          <button className="action-btn" title="Thông tin cuộc trò chuyện">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
            </svg>
          </button>
        </div>
      </div>

      <div className="messages-container">
        <div className="no-messages">
          <p>Chưa có tin nhắn nào trong cuộc trò chuyện này</p>
          <p>Hãy bắt đầu cuộc trò chuyện!</p>
        </div>
      </div>

      <div className="message-input-container">
        <div className="message-input">
          <button className="attach-btn" title="Đính kèm file">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M16.5 6v11.5c0 2.21-1.79 4-4 4s-4-1.79-4-4V5c0-1.38 1.12-2.5 2.5-2.5s2.5 1.12 2.5 2.5v10.5c0 .55-.45 1-1 1s-1-.45-1-1V6H10v9.5c0 1.38 1.12 2.5 2.5 2.5s2.5-1.12 2.5-2.5V5c0-2.21-1.79-4-4-4S7 2.79 7 5v12.5c0 3.04 2.46 5.5 5.5 5.5s5.5-2.46 5.5-5.5V6h-1.5z"/>
            </svg>
          </button>
          <input
            type="text"
            placeholder="Nhập tin nhắn..."
            disabled
          />
          <button className="send-btn" title="Gửi tin nhắn" disabled>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatWindow;
