import { useState, useEffect } from "react";
import { userAPI, conversationAPI } from "../../services/api";
import socketService from "../../services/socket";
import "./FriendsList.css";

const FriendsList = ({ onClose, onStartChat }) => {
  const [friends, setFriends] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [startingChat, setStartingChat] = useState(new Set());

  useEffect(() => {
    fetchFriends();
  }, []);

  const fetchFriends = async () => {
    try {
      setLoading(true);
      const response = await userAPI.getFriends();
      setFriends(response.data.friends);
    } catch (err) {
      setError("Không thể tải danh sách bạn bè");
      console.error("Error fetching friends:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleStartChat = async (friend) => {
    setStartingChat((prev) => new Set(prev).add(friend.id));

    try {
      // Tạo conversation 1-1 (không phải group)
      const response = await conversationAPI.create({
        isGroup: false,
        members: [friend.id], // Thêm friend vào conversation
      });

      const conversation = response.data;

      // Join conversation mới qua Socket.IO
      if (conversation?.id) {
        socketService.joinConversations([conversation.id]);
      }

      // Callback để chuyển đến conversation mới
      if (onStartChat) {
        onStartChat(conversation);
      }

      onClose();
    } catch (err) {
      // Nếu conversation đã tồn tại, có thể backend trả về conversation hiện có
      if (err.response?.data?.conversation) {
        const existingConversation = err.response.data.conversation;

        // Join existing conversation
        if (existingConversation?.id) {
          socketService.joinConversations([existingConversation.id]);
        }

        if (onStartChat) {
          onStartChat(existingConversation);
        }
        onClose();
      } else {
        alert(err.response?.data?.message || "Không thể tạo cuộc trò chuyện");
      }
    } finally {
      setStartingChat((prev) => {
        const newSet = new Set(prev);
        newSet.delete(friend.id);
        return newSet;
      });
    }
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const filteredFriends = friends.filter(
    (friend) =>
      friend.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      friend.phoneNumber.includes(searchTerm)
  );

  return (
    <div className="modal-backdrop" onClick={handleBackdropClick}>
      <div className="friends-list-modal">
        <div className="modal-header">
          <h3>Danh sách bạn bè</h3>
          <button className="close-btn" onClick={onClose}>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
            </svg>
          </button>
        </div>

        <div className="modal-body">
          {!loading && friends.length > 0 && (
            <div className="search-box">
              <svg
                className="search-icon"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z" />
              </svg>
              <input
                type="text"
                placeholder="Tìm kiếm bạn bè..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          )}

          {loading ? (
            <div className="loading-state">
              <div className="spinner"></div>
              <p>Đang tải danh sách bạn bè...</p>
            </div>
          ) : error ? (
            <div className="error-state">
              <svg
                width="48"
                height="48"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
              </svg>
              <p>{error}</p>
              <button className="retry-btn" onClick={fetchFriends}>
                Thử lại
              </button>
            </div>
          ) : friends.length === 0 ? (
            <div className="empty-state">
              <svg
                width="64"
                height="64"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z" />
              </svg>
              <h4>Chưa có bạn bè nào</h4>
              <p>Hãy tìm kiếm và kết bạn với những người bạn biết!</p>
            </div>
          ) : filteredFriends.length === 0 ? (
            <div className="empty-state">
              <svg
                width="48"
                height="48"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z" />
              </svg>
              <h4>Không tìm thấy bạn bè</h4>
              <p>Thử tìm kiếm với từ khóa khác</p>
            </div>
          ) : (
            <div className="friends-list">
              <div className="friends-count">
                {filteredFriends.length} bạn bè
                {searchTerm && ` (tìm kiếm: "${searchTerm}")`}
              </div>

              {filteredFriends.map((friend) => (
                <div key={friend.id} className="friend-item">
                  <div className="friend-avatar">
                    {friend.username.charAt(0).toUpperCase()}
                  </div>

                  <div className="friend-info">
                    <h4>{friend.username}</h4>
                    <p className="phone-number">{friend.phoneNumber}</p>
                  </div>

                  <div className="friend-actions">
                    <button
                      className="chat-btn"
                      onClick={() => handleStartChat(friend)}
                      disabled={startingChat.has(friend.id)}
                      title="Bắt đầu trò chuyện"
                    >
                      {startingChat.has(friend.id) ? (
                        <div className="spinner small"></div>
                      ) : (
                        <svg
                          width="18"
                          height="18"
                          viewBox="0 0 24 24"
                          fill="currentColor"
                        >
                          <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z" />
                        </svg>
                      )}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FriendsList;
