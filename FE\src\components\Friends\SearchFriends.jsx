import { useState } from 'react';
import { userAPI } from '../../services/api';
import './SearchFriends.css';

const SearchFriends = ({ onClose }) => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [searchResult, setSearchResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [sendingRequest, setSendingRequest] = useState(false);

  const handleSearch = async (e) => {
    e.preventDefault();
    
    if (!phoneNumber.trim()) {
      setError('Vui lòng nhập số điện thoại');
      return;
    }

    if (!/^[0-9]{10,11}$/.test(phoneNumber.trim())) {
      setError('Số điện thoại không hợp lệ (10-11 số)');
      return;
    }

    setLoading(true);
    setError('');
    setSearchResult(null);

    try {
      const response = await userAPI.searchByPhone(phoneNumber.trim());
      setSearchResult(response.data.user);
    } catch (err) {
      if (err.response?.status === 404) {
        setError('Không tìm thấy người dùng với số điện thoại này');
      } else {
        setError(err.response?.data?.message || 'Có lỗi xảy ra khi tìm kiếm');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSendFriendRequest = async () => {
    if (!searchResult) return;

    setSendingRequest(true);
    try {
      await userAPI.sendFriendRequest(searchResult.id);
      
      // Cập nhật trạng thái trong kết quả tìm kiếm
      setSearchResult(prev => ({
        ...prev,
        friendshipStatus: 'pending'
      }));
      
      alert('Đã gửi lời mời kết bạn thành công!');
    } catch (err) {
      alert(err.response?.data?.message || 'Không thể gửi lời mời kết bạn');
    } finally {
      setSendingRequest(false);
    }
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const getFriendshipStatusText = (status) => {
    switch (status) {
      case 'pending':
        return 'Đã gửi lời mời';
      case 'accepted':
        return 'Đã là bạn bè';
      case 'rejected':
        return 'Đã từ chối';
      default:
        return 'Gửi lời mời kết bạn';
    }
  };

  const canSendRequest = (status) => {
    return !status || status === 'none' || status === 'rejected';
  };

  return (
    <div className="modal-backdrop" onClick={handleBackdropClick}>
      <div className="search-friends-modal">
        <div className="modal-header">
          <h3>Tìm kiếm bạn bè</h3>
          <button className="close-btn" onClick={onClose}>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>

        <div className="modal-body">
          <form onSubmit={handleSearch}>
            <div className="search-form">
              <div className="form-group">
                <label htmlFor="phoneNumber">Số điện thoại:</label>
                <div className="input-group">
                  <input
                    type="tel"
                    id="phoneNumber"
                    value={phoneNumber}
                    onChange={(e) => {
                      setPhoneNumber(e.target.value);
                      setError('');
                      setSearchResult(null);
                    }}
                    placeholder="Nhập số điện thoại (VD: 0123456789)"
                    disabled={loading}
                  />
                  <button 
                    type="submit" 
                    className="search-btn"
                    disabled={loading}
                  >
                    {loading ? (
                      <div className="spinner"></div>
                    ) : (
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                      </svg>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </form>

          {error && (
            <div className="error-message">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
              {error}
            </div>
          )}

          {searchResult && (
            <div className="search-result">
              <div className="user-card">
                <div className="user-avatar">
                  {searchResult.username.charAt(0).toUpperCase()}
                </div>
                <div className="user-info">
                  <h4>{searchResult.username}</h4>
                  <p>{searchResult.phoneNumber}</p>
                </div>
                <div className="user-actions">
                  {canSendRequest(searchResult.friendshipStatus) ? (
                    <button 
                      className="friend-request-btn"
                      onClick={handleSendFriendRequest}
                      disabled={sendingRequest}
                    >
                      {sendingRequest ? (
                        <>
                          <div className="spinner small"></div>
                          Đang gửi...
                        </>
                      ) : (
                        <>
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M15 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm-9-2V7H4v3H1v2h3v3h2v-3h3v-2H6zm9 4c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                          </svg>
                          Kết bạn
                        </>
                      )}
                    </button>
                  ) : (
                    <div className="friendship-status">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                      </svg>
                      {getFriendshipStatusText(searchResult.friendshipStatus)}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          <div className="search-tips">
            <h4>Mẹo tìm kiếm:</h4>
            <ul>
              <li>Nhập đúng số điện thoại đã đăng ký</li>
              <li>Số điện thoại phải có 10-11 chữ số</li>
              <li>Không cần thêm dấu cách hoặc ký tự đặc biệt</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchFriends;
