/* Global Layout Styles for Full Browser Experience */

/* Reset và base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  height: 100%;
  overflow: hidden;
}

body {
  height: 100%;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

.App {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

/* Layout utilities */
.full-height {
  height: 100vh;
}

.full-width {
  width: 100vw;
}

.flex-container {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

.overflow-hidden {
  overflow: hidden;
}

.min-height-0 {
  min-height: 0;
}

.min-width-0 {
  min-width: 0;
}

.position-relative {
  position: relative;
}

.position-absolute {
  position: absolute;
}

.z-index-high {
  z-index: 1000;
}

/* Scrollbar styling cho toàn bộ ứng dụng */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

::-webkit-scrollbar-corner {
  background: #f1f1f1;
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

/* Responsive breakpoints */
@media (max-width: 1200px) {
  /* Large tablets và small desktops */
}

@media (max-width: 992px) {
  /* Tablets */
}

@media (max-width: 768px) {
  /* Small tablets và large phones */
  html, body {
    overflow-x: hidden;
  }
}

@media (max-width: 576px) {
  /* Small phones */
  html, body {
    overflow-x: hidden;
  }
}

@media (max-width: 480px) {
  /* Very small phones */
  html, body {
    overflow-x: hidden;
  }
}

/* Utility classes cho responsive */
.hidden-mobile {
  display: block;
}

.visible-mobile {
  display: none;
}

@media (max-width: 768px) {
  .hidden-mobile {
    display: none;
  }
  
  .visible-mobile {
    display: block;
  }
}

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.slide-in-left {
  animation: slideInLeft 0.3s ease-in-out;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.3s ease-in-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

/* Focus styles */
*:focus {
  outline: 2px solid #0084ff;
  outline-offset: 2px;
}

button:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid #0084ff;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  * {
    background: white !important;
    color: black !important;
  }
  
  .no-print {
    display: none !important;
  }
}
