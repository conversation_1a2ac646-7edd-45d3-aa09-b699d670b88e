.chat-page {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
  overflow: hidden;
}

.chat-header {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e4e6eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left h1 {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
  color: #1877f2;
  letter-spacing: -0.5px;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.username {
  font-size: 14px;
  color: #65676b;
  font-weight: 500;
}

.logout-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  background: #f0f2f5;
  color: #65676b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.logout-btn:hover {
  background: #e4e6eb;
  color: #1877f2;
}

.chat-content {
  flex: 1;
  display: flex;
  overflow: hidden;
  height: calc(100vh - 60px);
  min-height: 0;
  position: relative;
}

.mobile-overlay {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 99;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .chat-header {
    padding: 0 16px;
    height: 56px;
  }

  .header-left h1 {
    font-size: 20px;
  }

  .username {
    display: none;
  }

  .chat-content {
    height: calc(100vh - 56px);
  }

  .mobile-overlay {
    display: block;
  }
}

@media (max-width: 480px) {
  .chat-header {
    padding: 0 12px;
    height: 52px;
  }

  .header-left h1 {
    font-size: 18px;
  }

  .chat-content {
    height: calc(100vh - 52px);
  }
}
