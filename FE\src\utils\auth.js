// Auth utility functions
export const authUtils = {
  // Lưu token và user info vào localStorage
  setAuth: (token, user) => {
    localStorage.setItem('token', token);
    localStorage.setItem('user', JSON.stringify(user));
  },

  // Lấy token từ localStorage
  getToken: () => {
    return localStorage.getItem('token');
  },

  // Lấy user info từ localStorage
  getUser: () => {
    const user = localStorage.getItem('user');
    return user ? JSON.parse(user) : null;
  },

  // Kiểm tra user đã đăng nhập chưa
  isAuthenticated: () => {
    const token = localStorage.getItem('token');
    return !!token;
  },

  // Đăng xuất - xóa token và user info
  logout: () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  },

  // Decode JWT token để lấy thông tin (không verify signature)
  decodeToken: (token) => {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      return null;
    }
  },

  // Kiểm tra token có hết hạn không
  isTokenExpired: (token) => {
    const decoded = authUtils.decodeToken(token);
    if (!decoded || !decoded.exp) return true;
    
    const currentTime = Date.now() / 1000;
    return decoded.exp < currentTime;
  },
};
