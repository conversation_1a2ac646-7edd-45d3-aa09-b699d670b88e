/* Message Bubble Styles - Modern Chat Design */

/* Base message bubble styles */
.message-bubble {
  position: relative;
  padding: 8px 12px;
  border-radius: 18px;
  font-size: 14px;
  line-height: 1.4;
  word-wrap: break-word;
  white-space: pre-wrap;
  max-width: 100%;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Tin nhắn của mình - bên ph<PERSON>i */
.message-bubble.own {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1565c0;
  border-bottom-right-radius: 4px;
  margin-left: auto;
}

.message-bubble.own::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: -8px;
  width: 0;
  height: 0;
  border: 8px solid transparent;
  border-left-color: #bbdefb;
  border-bottom: 0;
  border-right: 0;
}

/* Tin nhắn nhận đ<PERSON> - bên tr<PERSON> */
.message-bubble.other {
  background: #ffffff;
  color: #1c1e21;
  border-bottom-left-radius: 4px;
  border: 1px solid #e4e6eb;
  margin-right: auto;
}

.message-bubble.other::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: -8px;
  width: 0;
  height: 0;
  border: 8px solid transparent;
  border-right-color: #ffffff;
  border-bottom: 0;
  border-left: 0;
}

/* Hover effects */
.message-bubble.own:hover {
  background: linear-gradient(135deg, #d1e7dd 0%, #a7d3f4 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2);
}

.message-bubble.other:hover {
  background: #f8f9fa;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Message status indicators */
.message-status {
  display: inline-flex;
  align-items: center;
  margin-left: 4px;
  opacity: 0.7;
}

.message-status.sent {
  color: #65676b;
}

.message-status.delivered {
  color: #1877f2;
}

.message-status.read {
  color: #42b883;
}

/* Time stamp styling */
.message-time {
  font-size: 11px;
  opacity: 0.8;
  margin-top: 2px;
  text-align: right;
}

.message-bubble.own .message-time {
  color: #1565c0;
}

.message-bubble.other .message-time {
  color: #65676b;
  text-align: left;
}

/* Consecutive message styling */
.message-bubble.consecutive {
  margin-top: 2px;
}

.message-bubble.consecutive.own {
  border-top-right-radius: 4px;
}

.message-bubble.consecutive.other {
  border-top-left-radius: 4px;
}

/* Animation for new messages */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.message-bubble.new {
  animation: messageSlideIn 0.3s ease-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .message-bubble {
    padding: 6px 10px;
    font-size: 13px;
    border-radius: 16px;
  }
  
  .message-bubble.own::after,
  .message-bubble.other::after {
    border-width: 6px;
  }
  
  .message-bubble.other::after {
    left: -6px;
  }
  
  .message-bubble.own::after {
    right: -6px;
  }
}

@media (max-width: 480px) {
  .message-bubble {
    padding: 5px 8px;
    font-size: 12px;
    border-radius: 14px;
  }
  
  .message-time {
    font-size: 10px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .message-bubble.other {
    background: #3a3b3c;
    color: #e4e6ea;
    border-color: #4e4f50;
  }
  
  .message-bubble.other::after {
    border-right-color: #3a3b3c;
  }
  
  .message-bubble.other:hover {
    background: #4e4f50;
  }
}

/* Selection styling */
.message-bubble::selection {
  background: rgba(24, 119, 242, 0.2);
}

.message-bubble.own::selection {
  background: rgba(21, 101, 192, 0.3);
}

/* Focus styles for accessibility */
.message-bubble:focus {
  outline: 2px solid #1877f2;
  outline-offset: 2px;
}

/* Link styling within messages */
.message-bubble a {
  color: inherit;
  text-decoration: underline;
  font-weight: 500;
}

.message-bubble.own a {
  color: #1565c0;
}

.message-bubble.other a {
  color: #1877f2;
}

.message-bubble a:hover {
  text-decoration: none;
}

/* Emoji sizing */
.message-bubble .emoji {
  font-size: 1.2em;
  vertical-align: middle;
}

/* Large emoji messages */
.message-bubble.emoji-only {
  background: transparent;
  box-shadow: none;
  padding: 4px;
  font-size: 2em;
}

.message-bubble.emoji-only::after {
  display: none;
}
