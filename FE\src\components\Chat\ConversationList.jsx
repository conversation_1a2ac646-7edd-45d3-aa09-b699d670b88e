import { useState, useEffect } from 'react';
import { conversationAPI } from '../../services/api';
import { authUtils } from '../../utils/auth';
import CreateGroupModal from './CreateGroupModal';
import './ConversationList.css';

const ConversationList = ({ onSelectConversation, selectedConversationId }) => {
  const [conversations, setConversations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const currentUser = authUtils.getUser();

  useEffect(() => {
    fetchConversations();
  }, []);

  const fetchConversations = async () => {
    try {
      setLoading(true);
      const response = await conversationAPI.getAll();
      setConversations(response.data);
    } catch (err) {
      setError('<PERSON>hông thể tải danh sách cuộc trò chuyện');
      console.error('Error fetching conversations:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateGroup = async (groupData) => {
    try {
      const response = await conversationAPI.create({
        name: groupData.name,
        isGroup: true
      });
      
      // Thêm conversation mới vào đầu danh sách
      setConversations(prev => [response.data, ...prev]);
      setShowCreateModal(false);
      
      // Auto select conversation mới tạo
      onSelectConversation(response.data);
    } catch (err) {
      console.error('Error creating group:', err);
      alert('Không thể tạo nhóm. Vui lòng thử lại.');
    }
  };

  const getConversationName = (conversation) => {
    if (conversation.isGroup) {
      return conversation.name || 'Nhóm không tên';
    } else {
      // Với chat 1-1, hiển thị tên của user khác
      const otherMember = conversation.memberships?.find(
        m => m.user.id !== currentUser?.id
      );
      return otherMember?.user.username || 'Người dùng';
    }
  };

  const getLastMessage = (conversation) => {
    const lastMessage = conversation.messages?.[0];
    if (!lastMessage) return 'Chưa có tin nhắn';
    
    return lastMessage.content.length > 50 
      ? lastMessage.content.substring(0, 50) + '...'
      : lastMessage.content;
  };

  const getLastMessageTime = (conversation) => {
    const lastMessage = conversation.messages?.[0];
    if (!lastMessage) return '';
    
    const messageDate = new Date(lastMessage.createdAt);
    const now = new Date();
    const diffInHours = (now - messageDate) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return messageDate.toLocaleTimeString('vi-VN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else {
      return messageDate.toLocaleDateString('vi-VN', { 
        day: '2-digit', 
        month: '2-digit' 
      });
    }
  };

  const filteredConversations = conversations.filter(conv =>
    getConversationName(conv).toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="conversation-list">
        <div className="conversation-header">
          <h2>Tin nhắn</h2>
        </div>
        <div className="loading">Đang tải...</div>
      </div>
    );
  }

  return (
    <div className="conversation-list">
      <div className="conversation-header">
        <h2>Tin nhắn</h2>
        <div className="header-actions">
          <button 
            className="create-group-btn"
            onClick={() => setShowCreateModal(true)}
            title="Tạo nhóm mới"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
            </svg>
          </button>
        </div>
      </div>

      <div className="search-box">
        <svg className="search-icon" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
        </svg>
        <input
          type="text"
          placeholder="Tìm kiếm"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {error && <div className="error-message">{error}</div>}

      <div className="conversations">
        {filteredConversations.length === 0 ? (
          <div className="no-conversations">
            <p>Chưa có cuộc trò chuyện nào</p>
            <button 
              className="create-first-group"
              onClick={() => setShowCreateModal(true)}
            >
              Tạo nhóm đầu tiên
            </button>
          </div>
        ) : (
          filteredConversations.map(conversation => (
            <div
              key={conversation.id}
              className={`conversation-item ${
                selectedConversationId === conversation.id ? 'active' : ''
              }`}
              onClick={() => onSelectConversation(conversation)}
            >
              <div className="conversation-avatar">
                {conversation.isGroup ? (
                  <div className="group-avatar">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A3.002 3.002 0 0 0 17 6c-1.66 0-3 1.34-3 3 0 .35.07.69.18 1H12c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3h2c0-2.76-2.24-5-5-5S7 1.24 7 4s2.24 5 5 5h2.18c.11.31.18.65.18 1v2H9v6h2v4h2v-4h2v4h2v-4h3z"/>
                    </svg>
                  </div>
                ) : (
                  <div className="user-avatar">
                    {getConversationName(conversation).charAt(0).toUpperCase()}
                  </div>
                )}
              </div>
              
              <div className="conversation-info">
                <div className="conversation-name">
                  {getConversationName(conversation)}
                </div>
                <div className="last-message">
                  {getLastMessage(conversation)}
                </div>
              </div>
              
              <div className="conversation-meta">
                <div className="last-time">
                  {getLastMessageTime(conversation)}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {showCreateModal && (
        <CreateGroupModal
          onClose={() => setShowCreateModal(false)}
          onCreateGroup={handleCreateGroup}
        />
      )}
    </div>
  );
};

export default ConversationList;
