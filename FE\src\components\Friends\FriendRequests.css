.friend-requests-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  animation: modalSlideIn 0.3s ease-out;
}

.loading-state, .error-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.loading-state .spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #0084ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-state p, .error-state p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.error-state svg, .empty-state svg {
  color: #ccc;
  margin-bottom: 16px;
}

.empty-state h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #333;
}

.empty-state p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.retry-btn {
  margin-top: 16px;
  padding: 10px 20px;
  background: #0084ff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.retry-btn:hover {
  background: #0066cc;
}

.requests-list {
  max-height: 400px;
  overflow-y: auto;
}

.request-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.request-item:hover {
  background: #f8f9fa;
}

.request-item:last-child {
  border-bottom: none;
}

.request-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 18px;
  color: white;
  flex-shrink: 0;
}

.request-info {
  flex: 1;
  min-width: 0;
}

.request-info h4 {
  margin: 0 0 4px 0;
  font-size: 15px;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.phone-number {
  margin: 0 0 2px 0;
  font-size: 13px;
  color: #666;
}

.request-time {
  margin: 0;
  font-size: 12px;
  color: #999;
}

.request-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.accept-btn, .reject-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.accept-btn {
  background: #28a745;
  color: white;
}

.accept-btn:hover:not(:disabled) {
  background: #218838;
  transform: scale(1.05);
}

.reject-btn {
  background: #dc3545;
  color: white;
}

.reject-btn:hover:not(:disabled) {
  background: #c82333;
  transform: scale(1.05);
}

.accept-btn:disabled, .reject-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.spinner.small {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Scrollbar styling */
.requests-list::-webkit-scrollbar {
  width: 6px;
}

.requests-list::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.requests-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.requests-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive */
@media (max-width: 480px) {
  .friend-requests-modal {
    width: 95%;
    margin: 20px;
  }
  
  .request-item {
    padding: 12px 16px;
  }
  
  .request-avatar {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
  
  .request-info h4 {
    font-size: 14px;
  }
  
  .phone-number {
    font-size: 12px;
  }
  
  .request-time {
    font-size: 11px;
  }
  
  .accept-btn, .reject-btn {
    width: 32px;
    height: 32px;
  }
}
