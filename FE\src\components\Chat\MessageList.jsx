import { useState, useEffect, useRef } from "react";
import { messageAPI } from "../../services/api";
import { authUtils } from "../../utils/auth";
import socketService from "../../services/socket";
import "./MessageList.css";

const MessageList = ({ conversation }) => {
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [typingUsers, setTypingUsers] = useState(new Set());
  const messagesEndRef = useRef(null);
  const currentUser = authUtils.getUser();

  useEffect(() => {
    if (conversation?.id) {
      fetchMessages();
    }
  }, [conversation?.id]);

  useEffect(() => {
    // Setup socket listeners
    const unsubscribeMessage = socketService.onMessage(handleNewMessage);
    const unsubscribeTyping = socketService.onTyping(handleTyping);

    return () => {
      unsubscribeMessage();
      unsubscribeTyping();
    };
  }, [conversation?.id]);

  useEffect(() => {
    // Auto scroll to bottom when new messages arrive
    scrollToBottom();
  }, [messages]);

  const fetchMessages = async () => {
    try {
      setLoading(true);
      setError("");
      const response = await messageAPI.getMessages(conversation.id);
      setMessages(response.data);
    } catch (err) {
      setError("Không thể tải tin nhắn");
      console.error("Error fetching messages:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleNewMessage = (message) => {
    // Chỉ add message nếu thuộc conversation hiện tại
    if (message.conversationId === conversation?.id) {
      setMessages((prev) => {
        // Kiểm tra message đã tồn tại chưa (tránh duplicate)
        const exists = prev.some((m) => m.id === message.id);
        if (exists) return prev;

        return [...prev, message];
      });
    }
  };

  const handleTyping = (data, action) => {
    if (data.conversationId !== conversation?.id) return;
    if (data.userId === currentUser?.id) return; // Không hiện typing của chính mình

    setTypingUsers((prev) => {
      const newSet = new Set(prev);
      if (action === "start") {
        newSet.add(data.username);
      } else {
        newSet.delete(data.username);
      }
      return newSet;
    });

    // Auto clear typing after 3 seconds
    if (action === "start") {
      setTimeout(() => {
        setTypingUsers((prev) => {
          const newSet = new Set(prev);
          newSet.delete(data.username);
          return newSet;
        });
      }, 3000);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const formatTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString("vi-VN", {
        hour: "2-digit",
        minute: "2-digit",
      });
    } else {
      return (
        date.toLocaleDateString("vi-VN", {
          day: "2-digit",
          month: "2-digit",
          year: "numeric",
        }) +
        " " +
        date.toLocaleTimeString("vi-VN", {
          hour: "2-digit",
          minute: "2-digit",
        })
      );
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return "Hôm nay";
    } else if (date.toDateString() === yesterday.toDateString()) {
      return "Hôm qua";
    } else {
      return date.toLocaleDateString("vi-VN");
    }
  };

  const shouldShowDateSeparator = (currentMessage, previousMessage) => {
    if (!previousMessage) return true;

    const currentDate = new Date(currentMessage.createdAt).toDateString();
    const previousDate = new Date(previousMessage.createdAt).toDateString();

    return currentDate !== previousDate;
  };

  const shouldShowHeader = (currentMessage, previousMessage) => {
    if (!previousMessage) return true;
    if (currentMessage.senderId !== previousMessage.senderId) return true;

    // Show header if messages are more than 5 minutes apart
    const currentTime = new Date(currentMessage.createdAt);
    const previousTime = new Date(previousMessage.createdAt);
    const diffMinutes = (currentTime - previousTime) / (1000 * 60);

    return diffMinutes > 5;
  };

  if (loading) {
    return (
      <div className="message-list loading">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Đang tải tin nhắn...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="message-list error">
        <div className="error-state">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
          </svg>
          <p>{error}</p>
          <button onClick={fetchMessages} className="retry-btn">
            Thử lại
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="message-list">
      <div className="messages-container">
        {messages.length === 0 ? (
          <div className="empty-messages">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
              <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z" />
            </svg>
            <h3>Chưa có tin nhắn nào</h3>
            <p>Hãy bắt đầu cuộc trò chuyện bằng cách gửi tin nhắn đầu tiên!</p>
          </div>
        ) : (
          <>
            {messages.map((message, index) => {
              const isOwnMessage = message.senderId === currentUser?.id;
              const showDateSeparator = shouldShowDateSeparator(
                message,
                messages[index - 1]
              );
              const showHeader = shouldShowHeader(message, messages[index - 1]);

              return (
                <div key={message.id}>
                  {showDateSeparator && (
                    <div className="date-separator">
                      <span>{formatDate(message.createdAt)}</span>
                    </div>
                  )}

                  <div className={`message ${isOwnMessage ? "own" : "other"}`}>
                    <div className="message-avatar">
                      {message.sender.username.charAt(0).toUpperCase()}
                    </div>

                    <div className="message-content">
                      {showHeader && (
                        <div className="message-header">
                          <div className="message-sender">
                            {message.sender.username}
                          </div>
                          <div className="message-timestamp">
                            {formatTime(message.createdAt)}
                          </div>
                        </div>
                      )}

                      <div className="message-text">{message.content}</div>
                    </div>
                  </div>
                </div>
              );
            })}
          </>
        )}

        {typingUsers.size > 0 && (
          <div className="typing-indicator">
            <div className="typing-avatar">
              <div className="typing-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
            <div className="typing-text">
              {Array.from(typingUsers).join(", ")} đang nhập...
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>
    </div>
  );
};

export default MessageList;
