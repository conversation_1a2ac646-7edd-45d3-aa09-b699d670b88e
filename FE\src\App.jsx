import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { authUtils } from "./utils/auth";
import Login from "./components/Auth/Login";
import Register from "./components/Auth/Register";
import ChatPage from "./components/Chat/ChatPage";
import ProtectedRoute from "./components/ProtectedRoute";
import "./App.css";

function App() {
  const isAuthenticated = authUtils.isAuthenticated();

  return (
    <Router>
      <div className="App">
        <Routes>
          {/* Public routes */}
          <Route
            path="/login"
            element={
              isAuthenticated ? <Navigate to="/chat" replace /> : <Login />
            }
          />
          <Route
            path="/register"
            element={
              isAuthenticated ? <Navigate to="/chat" replace /> : <Register />
            }
          />

          {/* Protected routes */}
          <Route
            path="/chat"
            element={
              <ProtectedRoute>
                <ChatPage />
              </ProtectedRoute>
            }
          />

          {/* Default redirect */}
          <Route
            path="/"
            element={
              <Navigate to={isAuthenticated ? "/chat" : "/login"} replace />
            }
          />

          {/* Catch all other routes */}
          <Route
            path="*"
            element={
              <Navigate to={isAuthenticated ? "/chat" : "/login"} replace />
            }
          />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
